#!/bin/bash

get_token()
{
    # CLIENT_ID=$(az appconfig kv list \
    #  --subscription "$SUBSCRIPTION" \
    #  --name "$APPCONFIG_NAME" \
    #  --label "$ENV" \
    #  --key "IdentitySettings:ApiSettings:identity-server-api:ClientId" \
    #  --query "[0].value" \
    #  --output tsv)

    # CLIENT_SECRET=$(az appconfig kv list \
    #  --subscription "$SUBSCRIPTION" \
    #  --name "$APPCONFIG_NAME" \
    #  --label "$ENV" \
    #  --key "IdentitySettings:ApiSettings:identity-server-api:ClientSecret" \
    #  --query "[0].value" \
    #  --output tsv)

    CLIENT_ID=Identity.Server.B2B.Client
    CLIENT_SECRET=UV952KYGQNRLQ7MJ

    curl -s -X POST "${IDENTITY_SERVER}/connect/token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=client_credentials" \
    -d "client_id=${CLIENT_ID}" \
    -d "client_secret=${CLIENT_SECRET}" \
    -d "scope=IdentityServerApi" | jq -r '.access_token'
}

get_secret()
{
    CREATED_DATE=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")
    EXPIRY_DATE=$(date -u -v+6m +"%Y-%m-%dT%H:%M:%S.000Z")
    DESCRIPTION=$(date -u -v+6m +"%Y-%m-%d")

    curl -s "${IDENTITY_SERVER}/v1/Clients/${API}/secrets" \
    -H 'accept: */*' \
    -H "Authorization: Bearer ${TOKEN}" \
    -H 'Content-Type: application/json' \
    -d "{
    \"description\": \"AutoRotation ${DESCRIPTION}\",
    \"expiryDate\": \"${EXPIRY_DATE}\",
    \"createdDate\": \"${CREATED_DATE}\",
    \"type\": \"SharedSecret\"
    }" | jq -r '.value'
}

update_secret()
{
    API_NAME=$(az appconfig kv list \
      --subscription "$SUBSCRIPTION" \
      --name "$APPCONFIG_NAME" \
      --label "$ENV" \
      --key "IdentitySettings:ApiSettings:*" \
      --query "[].{key:key, value:value}" \
      --output json \
      --all | \
      jq -r --arg val "$API" 'map(select(.value == $val)) | .[] | .key | split(":") | .[2]')

    KEY_NAME="IdentitySettings:ApiSettings:${API_NAME}:ClientSecret"

    if [[ "$ENV" == "Development" || "$ENV" == "Staging" ]]; then
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "WBC-Appconfig-Development" \
        --label "Development" \
        --key "$KEY_NAME" \
        --value "$SECRET" \
        --yes
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "WBC-Appconfig-Staging" \
        --label "Staging" \
        --key "$KEY_NAME" \
        --value "$SECRET" \
        --yes
    else
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "$APPCONFIG_NAME" \
        --label "$ENV" \
        --key "$KEY_NAME" \
        --value "$SECRET" \
        --yes
    fi
}

usage()
{
    echo "usage: `basename $0`: <API_CLIENT_ID> <Environment>"
    exit 1
}

[ $# -ne 2 ] && usage

API=$1
ENV=$2

case $ENV in
  Development)
    APPCONFIG_NAME="WBC-Appconfig-Development"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.development.webuycars.co.za/identity-server-api"
    ;;
  Staging)
    APPCONFIG_NAME="WBC-Appconfig-Staging"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.staging.webuycars.co.za/identity-server-api"
    ;;
  Staging.NAM)
    APPCONFIG_NAME="WBC-Appconfig-Staging"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.staging.webuycars.na/identity-server-api"
    ;;
  Production)
    APPCONFIG_NAME="WBC-Appconfig-Prod"
    IDENTITY_SERVER="https://identity.webuycars.co.za"
    SUBSCRIPTION="WeBuyCars PROD"
    ;;
  NAM)
    APPCONFIG_NAME="WBC-Appconfig-Prod"
    IDENTITY_SERVER="https://appgateway.webuycars.na/identity-server-api"
    ;;
  *)
    echo "Unknown environment: $ENV"
    exit 1
    ;;
esac

TOKEN=$(get_token)
SECRET=$(get_secret)
echo $1 $SECRET
update_secret
