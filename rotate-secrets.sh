#!/bin/bash

usage()
{
    echo "usage: `basename $0`: <API_NAME> [Environment]"
    echo "  API_NAME: The name of the API to rotate secrets for (e.g., 'identity-server-api')"
    echo "  Environment: Development (default), Staging, Staging.NAM, Production, NAM"
    exit 1
}

# Check if API_NAME is provided
[ $# -lt 1 ] && usage

API_NAME=$1
ENV=${2:-Development}

get_token()
{
    CLIENT_ID=$(az appconfig kv list \
     --subscription "$SUBSCRIPTION" \
     --name "$APPCONFIG_NAME" \
     --label "$ENV" \
     --key "IdentitySettings:ApiSettings:identity-server-api:ClientId" \
     --query "[0].value" \
     --output tsv)

    CLIENT_SECRET=$(az appconfig kv list \
     --subscription "$SUBSCRIPTION" \
     --name "$APPCONFIG_NAME" \
     --label "$ENV" \
     --key "IdentitySettings:ApiSettings:identity-server-api:ClientSecret" \
     --query "[0].value" \
     --output tsv)

    # Fallback to hardcoded values if appconfig lookup fails
    if [[ -z "$CLIENT_ID" ]]; then
        CLIENT_ID=Identity.Server.B2B.Client
    fi
    if [[ -z "$CLIENT_SECRET" ]]; then
        CLIENT_SECRET=UV952KYGQNRLQ7MJ
    fi

    curl -s -X POST "${IDENTITY_SERVER}/connect/token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=client_credentials" \
    -d "client_id=${CLIENT_ID}" \
    -d "client_secret=${CLIENT_SECRET}" \
    -d "scope=IdentityServerApi" | jq -r '.access_token'
}

get_client_id()
{
    # Get the ClientId for the specified API_NAME from Azure App Configuration
    CLIENT_ID=$(az appconfig kv list \
      --subscription "$SUBSCRIPTION" \
      --name "$APPCONFIG_NAME" \
      --label "$ENV" \
      --key "IdentitySettings:ApiSettings:${API_NAME}:ClientId" \
      --query "[0].value" \
      --output tsv)

    if [[ -z "$CLIENT_ID" ]]; then
        echo "Error: Could not find ClientId for API_NAME: ${API_NAME} in environment: ${ENV}"
        exit 1
    fi

    echo "$CLIENT_ID"
}

get_secret()
{
    CREATED_DATE=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")
    EXPIRY_DATE=$(date -u -v+6m +"%Y-%m-%dT%H:%M:%S.000Z")
    DESCRIPTION=$(date -u -v+6m +"%Y-%m-%d")

    curl -s "${IDENTITY_SERVER}/v1/Clients/${API_CLIENT_ID}/secrets" \
    -H 'accept: */*' \
    -H "Authorization: Bearer ${TOKEN}" \
    -H 'Content-Type: application/json' \
    -d "{
    \"description\": \"AutoRotation ${DESCRIPTION}\",
    \"expiryDate\": \"${EXPIRY_DATE}\",
    \"createdDate\": \"${CREATED_DATE}\",
    \"type\": \"SharedSecret\"
    }" | jq -r '.value'
}

update_secret()
{
    KEY_NAME="IdentitySettings:ApiSettings:${API_NAME}:ClientSecret"

    echo "Updating secret for ${API_NAME} in environment ${ENV}..."
    echo "Key: ${KEY_NAME}"

    if [[ "$ENV" == "Development" || "$ENV" == "Staging" ]]; then
      echo "Updating Development environment..."
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "WBC-Appconfig-Development" \
        --label "Development" \
        --key "$KEY_NAME" \
        --value "$SECRET" \
        --yes

      echo "Updating Staging environment..."
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "WBC-Appconfig-Staging" \
        --label "Staging" \
        --key "$KEY_NAME" \
        --value "$SECRET" \
        --yes
    else
      echo "Updating ${ENV} environment..."
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "$APPCONFIG_NAME" \
        --label "$ENV" \
        --key "$KEY_NAME" \
        --value "$SECRET" \
        --yes
    fi

    echo "Secret rotation completed successfully for ${API_NAME}"
}

case $ENV in
  Development)
    APPCONFIG_NAME="WBC-Appconfig-Development"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.development.webuycars.co.za/identity-server-api"
    ;;
  Staging)
    APPCONFIG_NAME="WBC-Appconfig-Staging"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.staging.webuycars.co.za/identity-server-api"
    ;;
  Staging.NAM)
    APPCONFIG_NAME="WBC-Appconfig-Staging"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.staging.webuycars.na/identity-server-api"
    ;;
  Production)
    APPCONFIG_NAME="WBC-Appconfig-Prod"
    IDENTITY_SERVER="https://identity.webuycars.co.za"
    SUBSCRIPTION="WeBuyCars PROD"
    ;;
  NAM)
    APPCONFIG_NAME="WBC-Appconfig-Prod"
    IDENTITY_SERVER="https://appgateway.webuycars.na/identity-server-api"
    ;;
  *)
    echo "Unknown environment: $ENV"
    exit 1
    ;;
esac

# Main execution
echo "Starting secret rotation for API: ${API_NAME} in environment: ${ENV}"

# Get the ClientId for the specified API_NAME
API_CLIENT_ID=$(get_client_id)
echo "Found ClientId: ${API_CLIENT_ID}"

# Get authentication token
TOKEN=$(get_token)
if [[ -z "$TOKEN" ]]; then
    echo "Error: Failed to get authentication token"
    exit 1
fi

# Generate new secret
SECRET=$(get_secret)
if [[ -z "$SECRET" ]]; then
    echo "Error: Failed to generate new secret"
    exit 1
fi

echo "Generated new secret for ${API_NAME} : ${SECRET}"

# Update the secret in Azure App Configuration
update_secret