[{"key": "IdentitySettings:ApiSettings::ClientSecret", "value": "mmNxCK/p6PyFS03bQ3CnIfffTw/9u5xmpEnheGgOt6o="}, {"key": "IdentitySettings:ApiSettings:allocation-manager-api:ClientId", "value": "AllocationEngine.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:allocation-manager-api:ClientSecret", "value": "lX0rU9ybrrNtR9ul8d4MGTvSgypIoNtdVTL4Pt7ep+w="}, {"key": "IdentitySettings:ApiSettings:auction-api:ClientId", "value": "Auctions.WBC.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:auction-api:ClientSecret", "value": "oEPHe/z4qxVH7FuXwh0aHcQLeFSUEYfumRt00zm49/E="}, {"key": "IdentitySettings:ApiSettings:auction-platform:ClientId", "value": "AE.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:auction-platform:ClientSecret", "value": "SgHoK9rohbMTfUZO2ZcDSvO2J1N+aH4UjVoY2Kp+iTo="}, {"key": "IdentitySettings:ApiSettings:auction-signalr-api:ClientId", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:auction-signalr-api:ClientSecret", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:audatex-api:ClientId", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:audatex-api:ClientSecret", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:autotrader-upload-service:ClientId", "value": "Autotrader.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:autotrader-upload-service:ClientSecret", "value": "EJU/NwefBDPHduGYrJyBNfThYq9GQJiWwyV5u2KX6/Y="}, {"key": "IdentitySettings:ApiSettings:banking-api:ClientId", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:banking-api:ClientSecret", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:blms-api:ClientId", "value": "BuyLeadManagementSystem.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:blms-api:ClientSecret", "value": "WRDbj+sDyjEI1zpwsVvSwBKQ5P1+Qmp8KUNGAfO4/Rc="}, {"key": "IdentitySettings:ApiSettings:buy-lead-management-api:ClientId", "value": "BuyLeadManagementSystem.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:buy-lead-management-api:ClientSecret", "value": "WRDbj+sDyjEI1zpwsVvSwBKQ5P1+Qmp8KUNGAfO4/Rc="}, {"key": "IdentitySettings:ApiSettings:cancellations-api:ClientId", "value": "Cancellation.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:cancellations-api:ClientSecret", "value": "2iBVhCkLQGEl0crPOs4vmHwznO9wWWH3bHInuZr2nSE="}, {"key": "IdentitySettings:ApiSettings:chat-api:ClientId", "value": "Chat.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:chat-api:ClientSecret", "value": "tpQhsrsT+tKqJ02M/QJwCf88/mo8H4x4RdgTxtOWUrE="}, {"key": "IdentitySettings:ApiSettings:cntrl-api:ClientId", "value": "Ctrl.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:cntrl-api:ClientSecret", "value": "fcI4HHG8hIpMBItMgAfYd4+NOLdOJhSJrrDFbxtXfSU="}, {"key": "IdentitySettings:ApiSettings:communications-api:ClientId", "value": "Comms.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:communications-api:ClientSecret", "value": "kxm/R8n7Yf4CglVGIvjnNzDy5gUkJk8u5Wr2pQwVXSg="}, {"key": "IdentitySettings:ApiSettings:complaint-management-api:ClientId", "value": "Complaints.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:complaint-management-api:ClientSecret", "value": "/e+IpNpQAEWwWWGo0nwg0UuSUrOUx2jOgySaaZUojCw="}, {"key": "IdentitySettings:ApiSettings:crm-api:ClientId", "value": "CRM.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:crm-api:ClientSecret", "value": "9zGmaVAuLoASkHGiODwN16QxrSYT+8EmImUAqrqrD1A="}, {"key": "IdentitySettings:ApiSettings:documentmanagement-api:ClientId", "value": "DocumentManagement.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:documentmanagement-api:ClientSecret", "value": "jlbtgX5lgQcv3eSmP2xsCkENnuErUR0Tn2i88j3zZyU="}, {"key": "IdentitySettings:ApiSettings:documentsign-api:ClientId", "value": "DocumentSign.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:documentsign-api:ClientSecret", "value": "kZaZk1zOiav4ej9Hgsqn6YhQryJnbzL+abnc/fwtz5Y="}, {"key": "IdentitySettings:ApiSettings:driver-assignment-api:ClientId", "value": "DriverAssignment.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:driver-assignment-api:ClientSecret", "value": "TR6X2Kwecbdx7yPr2/iOo4RsTneKYWO1D9lzFtROzoY="}, {"key": "IdentitySettings:ApiSettings:ecu-api:ClientId", "value": "ECU.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:ecu-api:ClientSecret", "value": "VjEURkkYBpTDTwqFKbNUmV+K/TAZfk96dYFufKts81g="}, {"key": "IdentitySettings:ApiSettings:enatis-rtmc-api:ClientId", "value": "RTMC.eNatis.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:enatis-rtmc-api:ClientSecret", "value": "K710VGC9gHBzLtG/7RWKQ2nJUsVnyDMTk/e9nfH4hMo="}, {"key": "IdentitySettings:ApiSettings:evaluation-api:ClientId", "value": "Evaluation.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:evaluation-api:ClientSecret", "value": "dAI0+e+7Z7b3sZYIumX3lts+7HhyYLWt1+OKLrz+iY8="}, {"key": "IdentitySettings:ApiSettings:evolve-api:ClientId", "value": "Evolve.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:evolve-api:ClientSecret", "value": "6DV1Pskwuso3uNDhCrHOOCOpnWNE8Rlg7EA/9bqWKcE="}, {"key": "IdentitySettings:ApiSettings:external-feeds-api:ClientId", "value": "ExternalFeeds.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:external-feeds-api:ClientSecret", "value": "Wnxmc3b4+3mx1Ze8iAHVYMvGqnMC72fXO09ousUuM+s="}, {"key": "IdentitySettings:ApiSettings:feed-experiments-api:ClientId", "value": "Feed.Experiments.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:feed-experiments-api:ClientSecret", "value": "Nt+tU+HS4egu9qQ14OuYs6MzqyUokmnACs2Vn6MoKdM="}, {"key": "IdentitySettings:ApiSettings:finance-applications-api:ClientId", "value": "FinanceApplication.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:finance-applications-api:ClientSecret", "value": "b5HZRSHhWX55wJOCWimNassTaB3Tu83rYYs7RD0UN/c="}, {"key": "IdentitySettings:ApiSettings:financial-api:ClientId", "value": "Financial.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:financial-api:ClientSecret", "value": "Xm9eaXZCVATkPW/2LRJOZrnSop24CLVuE8nrO98forw="}, {"key": "IdentitySettings:ApiSettings:floorplan-api:ClientId", "value": "Floorplan.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:floorplan-api:ClientSecret", "value": "mSvapjk7YyJHdMR6NJgmmi+8qngsjwZLWz9kx6uY/0o="}, {"key": "IdentitySettings:ApiSettings:forms-api:ClientId", "value": "Forms-API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:forms-api:ClientSecret", "value": "buF7ybi1BK1LCblkMyuNUf7bv0P6q6k8xRRC2rQq834="}, {"key": "IdentitySettings:ApiSettings:gps-tracking-api:ClientId", "value": "GPS.Tracking.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:gps-tracking-api:ClientSecret", "value": "Da0fjCN2Ld7xz6dBw0Wz4FvELJzP61HH/lYMVNDbxKs="}, {"key": "IdentitySettings:ApiSettings:identity-server-api-public:ClientId", "value": "Identity.Server.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:identity-server-api-public:ClientSecret", "value": "/nf98gRJBvLq9p4NFGd2tqDS5QhyKHkbmxblxxT14Fs="}, {"key": "IdentitySettings:ApiSettings:identity-server-api:ClientId", "value": "Identity.Server.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:identity-server-api:ClientSecret", "value": "/nf98gRJBvLq9p4NFGd2tqDS5QhyKHkbmxblxxT14Fs="}, {"key": "IdentitySettings:ApiSettings:ims-api:ClientId", "value": "IMS.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:ims-api:ClientSecret", "value": "DuMdEsiqGax7UouoWGUbUH3CgGP9gJMeVWpAzhG04OA="}, {"key": "IdentitySettings:ApiSettings:inspectify-api:ClientId", "value": "Inspectify-API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:inspectify-api:ClientSecret", "value": "4Zk67bhRh4UngFDn3YKsgm6BhX8y+TZ0gxvwQMUv0r4="}, {"key": "IdentitySettings:ApiSettings:inventory-management-api:ClientId", "value": "IMS.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:inventory-management-api:ClientSecret", "value": "Fj8IlyKcVBrPHHf9bzp5eu+zEKJ2vCcdesmphX06TFw="}, {"key": "IdentitySettings:ApiSettings:kyc-api:ClientId", "value": "KYC.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:kyc-api:ClientSecret", "value": "YDg5JDksUqKoAep5K1rz0uwmtFSzbhWNJaVhJZUODl4="}, {"key": "IdentitySettings:ApiSettings:logistics-api:ClientId", "value": "Logistics.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:logistics-api:ClientSecret", "value": "bDde0TWb5CIyWV3ZgW1MVltxjveNEzJCnvYYRSatDJg="}, {"key": "IdentitySettings:ApiSettings:media-api:ClientId", "value": "Media.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:media-api:ClientSecret", "value": "TW9BdIxDpbn/mpcsRCaig9lTEdRSL6Sl2BxaZD9/zqI="}, {"key": "IdentitySettings:ApiSettings:mobalyz-api:ClientId", "value": "Mobalyz.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:mobalyz-api:ClientSecret", "value": "BCyQw0EkYia/PxU37JgeB+mnV3adOaM4yX8EUnpMF5U="}, {"key": "IdentitySettings:ApiSettings:mobilebuildmanagement-api:ClientId", "value": "MobileBuildManagement.API.Test.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:mobilebuildmanagement-api:ClientSecret", "value": "4yLTU0RVqiUv+CddWED/MM+15UsS7vbeWyk1PNt4xvg="}, {"key": "IdentitySettings:ApiSettings:natis-api:ClientId", "value": "Natis.API.Test.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:natis-api:ClientSecret", "value": "oarFNSl/k9bLT7siRbsY6NCln+QBKSrdnMAdkAFzk4Q="}, {"key": "IdentitySettings:ApiSettings:netstar-api:ClientId", "value": "Netstar.API.Test.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:netstar-api:ClientSecret", "value": "hQXgYGzWhM1qVdjYcSF8B+G2NIPYyuyOQ9qYmTabcp8="}, {"key": "IdentitySettings:ApiSettings:nexus-api:ClientId", "value": "Nexus.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:nexus-api:ClientSecret", "value": "pHUcHZJ4DuTbkBs47XPfuvdCROP6URnCum2Htv8/bYE="}, {"key": "IdentitySettings:ApiSettings:odometer-management-api:ClientId", "value": "Odometer.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:odometer-management-api:ClientSecret", "value": "ApAwrHHO0XKbhc4Qi+PDCShogaFi+iZK98ogd/LX/tQ="}, {"key": "IdentitySettings:ApiSettings:organization-api:ClientId", "value": "Organization.API.Test.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:organization-api:ClientSecret", "value": "cyGj6YpvG0R7PNTuATB8fPBXnHBgsTYomusGDQ2qt6w="}, {"key": "IdentitySettings:ApiSettings:outsurance-api:ClientId", "value": "OUTsurance.API.Test.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:outsurance-api:ClientSecret", "value": "fcnIW/+V3rZU/2VooT94vV6HWi5wj52CwBnNRnnfH+U="}, {"key": "IdentitySettings:ApiSettings:photo-booth-api:ClientId", "value": "PhotoBooth.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:photo-booth-api:ClientSecret", "value": "81gjJP4kYKEQGLpGPaQMQw5voNvS+DPgzH7D3WNGI18="}, {"key": "IdentitySettings:ApiSettings:pricecards-api:ClientId", "value": "PriceCards.Test.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:pricecards-api:ClientSecret", "value": "/+dBpr4sHi0R8+naDimQiT9R0qdKLRsZGbojqRuK1BA="}, {"key": "IdentitySettings:ApiSettings:pricing-api:ClientId", "value": "Pricing.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:pricing-api:ClientSecret", "value": "y5EOskiGpH6I4rGfe+0fkr8W7Cp+44fOo1IcxKVRldM="}, {"key": "IdentitySettings:ApiSettings:processengine:ClientId", "value": "ProcessEngine.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:processengine:ClientSecret", "value": "hEeJop8F8yMtFxA8BSXBpS5GxGu4S9o+OFygiRZrFT0="}, {"key": "IdentitySettings:ApiSettings:reporting-api:ClientId", "value": "Reporting.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:reporting-api:ClientSecret", "value": "zLHkD94KJ1RJ0xdRFoM8sUJ2IXX+i34WrDe+ZJ1juQk="}, {"key": "IdentitySettings:ApiSettings:rfid-api:ClientId", "value": "RFID.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:rfid-api:ClientSecret", "value": "9XFHbJUWJ4UNXb4iBnmwhXM2IRZIjQbs8gn3hqYCsLQ="}, {"key": "IdentitySettings:ApiSettings:roadworthy-api:ClientId", "value": "Roadworhty.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:roadworthy-api:ClientSecret", "value": "HZIalGQ9VpP40BFpQHyoGwmA0Sg/JMpCzZi5xzSNyH4="}, {"key": "IdentitySettings:ApiSettings:sales-catalogue-api-public:ClientId", "value": "Sales.Catalogue.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:sales-catalogue-api-public:ClientSecret", "value": "FpgHMRevtr9iPYTmu/2nUYP5W+9xrq0mLUXeQKNSi4k="}, {"key": "IdentitySettings:ApiSettings:sales-catalogue-api:ClientId", "value": "Sales.Catalogue.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:sales-catalogue-api:ClientSecret", "value": "FpgHMRevtr9iPYTmu/2nUYP5W+9xrq0mLUXeQKNSi4k="}, {"key": "IdentitySettings:ApiSettings:scheduling-api:ClientId", "value": "Schedule.APP.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:scheduling-api:ClientSecret", "value": "hJintu/idIT1xaX2JwHO5SYum7b8YUBu0acsxrOiDF8="}, {"key": "IdentitySettings:ApiSettings:settings-api:ClientId", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:settings-api:ClientSecret", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:slms-api:ClientId", "value": "Leads.API.Test.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:slms-api:ClientSecret", "value": "mmNxCK/p6PyFS03bQ3CnIfffTw/9u5xmpEnheGgOt6o="}, {"key": "IdentitySettings:ApiSettings:targets-api:ClientId", "value": "Target.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:targets-api:ClientSecret", "value": "9UKPjczhx7cEa66Xri2B9dxuthEwtDoplPJZ78h2ymo="}, {"key": "IdentitySettings:ApiSettings:timeandattendance-api:ClientId", "value": "TimeAndAttendance.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:timeandattendance-api:ClientSecret", "value": "fxTNKRsKyNQfZ+mR4FQTfqoUZgjpjM/f6Ac4vF6mzOo="}, {"key": "IdentitySettings:ApiSettings:tracking-api:ClientId", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:tracking-api:ClientSecret", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:tyremart-api:ClientId", "value": "TyreMart.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:tyremart-api:ClientSecret", "value": "j2/HtrkOUO09whC5ieHbPj51tGBl2dkVMQrP1YFygbw="}, {"key": "IdentitySettings:ApiSettings:usermanagement-api:ClientId", "value": "UserManagement.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:usermanagement-api:ClientSecret", "value": "5lZ82rms91E2Ac302paeDkcOQOWq3O8c9HhVzG0H3xQ="}, {"key": "IdentitySettings:ApiSettings:vaps-api:ClientId", "value": "Vaps.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:vaps-api:ClientSecret", "value": "rOuzW6JmGWVXlzifpIpKNZLUwXMg63Wos9vlIlOSPQ0="}, {"key": "IdentitySettings:ApiSettings:vehicle-catalogue-api:ClientId", "value": "Vehicle.Catalogue.API.Test.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:vehicle-catalogue-api:ClientSecret", "value": "lAqbTq8hiCx1APPK2cixCAdcstJvLHe2uxXLggIGhz8="}, {"key": "IdentitySettings:ApiSettings:vehicle-expense-management-api:ClientId", "value": "Expense.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:vehicle-expense-management-api:ClientSecret", "value": "/EKr1r1BSK2ZFnA8SPOWxz0I7lMyhA9fCzqPL+1r/FE="}, {"key": "IdentitySettings:ApiSettings:voip-api:ClientId", "value": "VoIP.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:voip-api:ClientSecret", "value": "Oa86re445a1W6VWrR656FoT6iQgCGDkA4xgRNBo/x2A="}, {"key": "IdentitySettings:ApiSettings:wallet-api:ClientId", "value": "Wallet.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:wallet-api:ClientSecret", "value": "VLErThWJ6AE6K2RdVMDYTHwUTgwk3bB1yaYwk5dqF0E="}, {"key": "IdentitySettings:ApiSettings:website-auction-backend:ClientId", "value": "Website.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:website-auction-backend:ClientSecret", "value": "3sNycgfSlpEK9HaebM1C4ivsKy0BvkqzVaRKeWgk+yw="}, {"key": "IdentitySettings:ApiSettings:website-backend:ClientId", "value": "Website.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:website-backend:ClientSecret", "value": "A3ZnHWIK3QwFnIhH+8IAWq6PmteVkxZ0JZIR952U3Y0="}, {"key": "IdentitySettings:ApiSettings:wefin-api:ClientId", "value": "WeFin.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:wefin-api:ClientSecret", "value": "YyWIH3Lr+0qz7R8Q1B/nYzX9JPJnFG1FeHdNO60BsaY="}, {"key": "IdentitySettings:ApiSettings:wefin-signio-api:ClientId", "value": "Signio.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:wefin-signio-api:ClientSecret", "value": "eb+pxYQaq1DIANZEKxju6xA3KYJWPPdYrCXEGbpA0pg="}, {"key": "IdentitySettings:ApiSettings:wesupport-api:ClientId", "value": "WeSupport.WBC.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:wesupport-api:ClientSecret", "value": "AFALbKwaaeWH/IMw/0kZs5dZ7Bxnj7ACAhn2HLR3Rsc="}]