[{"key": "IdentitySettings:ApiSettings::ClientSecret", "value": "44EnsMeCvuiKLyZY2Bu03tVssdY//DgmyVr9Pui2MAI="}, {"key": "IdentitySettings:ApiSettings:allocation-manager-api:ClientId", "value": "AllocationEngine.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:allocation-manager-api:ClientSecret", "value": "F+6GkheL9dpTFaDzoSG1295olWu9wZJmcc/65rQ9osg="}, {"key": "IdentitySettings:ApiSettings:auction-api:ClientId", "value": "Auctions.WBC.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:auction-api:ClientSecret", "value": "lEIZouYHlGpVT5Guo+SrQFnAOFYqTIuEorKD/UfQd/o="}, {"key": "IdentitySettings:ApiSettings:auction-platform:ClientId", "value": "AE.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:auction-platform:ClientSecret", "value": "/pI5sDpbcCghbHbVeZj0atDn/3L2IhdCsE68/hB1GDM="}, {"key": "IdentitySettings:ApiSettings:auction-signalr-api:ClientId", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:auction-signalr-api:ClientSecret", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:audatex-api:ClientId", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:audatex-api:ClientSecret", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:autotrader-upload-service:ClientId", "value": "Autotrader.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:autotrader-upload-service:ClientSecret", "value": "TW1lGXtoqhkL02ml/qCwWZ1aOpN2ZtTgkhJlioZOWRg="}, {"key": "IdentitySettings:ApiSettings:banking-api:ClientId", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:banking-api:ClientSecret", "value": "notset"}, {"key": "IdentitySettings:ApiSettings:blms-api:ClientId", "value": "BuyLeadManagementSystem.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:blms-api:ClientSecret", "value": "eFt2XNdJ/bY/dywIHxZLu27o1i9JOnZmN8eh5/sgcDc="}, {"key": "IdentitySettings:ApiSettings:buy-lead-management-api:ClientId", "value": "BuyLeadManagementSystem.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:buy-lead-management-api:ClientSecret", "value": "eFt2XNdJ/bY/dywIHxZLu27o1i9JOnZmN8eh5/sgcDc="}, {"key": "IdentitySettings:ApiSettings:cancellations-api:ClientId", "value": "Cancellation.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:cancellations-api:ClientSecret", "value": "mQ7yEukC7Deq8gS3bI4qPC451ayJWkGxRqEn9Fsfe+0="}, {"key": "IdentitySettings:ApiSettings:chat-api:ClientId", "value": "Chat.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:chat-api:ClientSecret", "value": "GsV7U2/heG6yVTWDkI43LVGvOhCZk0Vea9D93QY1Ec8="}, {"key": "IdentitySettings:ApiSettings:cntrl-api:ClientId", "value": "Ctrl.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:cntrl-api:ClientSecret", "value": "ULRWJoFVthuvSt8kuP3qdqXjcs7dX2VD1K5x4DhU2NY="}, {"key": "IdentitySettings:ApiSettings:communications-api:ClientId", "value": "Comms.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:communications-api:ClientSecret", "value": "cffneN1z1KFoWTjAszX8VjJQCYaLs7ufkgKYjtXELxU="}, {"key": "IdentitySettings:ApiSettings:complaint-management-api:ClientId", "value": "Complaints.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:complaint-management-api:ClientSecret", "value": "yZ8ZsG8mP13ZccZrYkdI+TaX4/+bcLq1QcfOvp27Xks="}, {"key": "IdentitySettings:ApiSettings:crm-api:ClientId", "value": "CRM.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:crm-api:ClientSecret", "value": "T7M+m3I5T8dXbz/djXL5lmOOs308lwkAH0IvPoJlwU8="}, {"key": "IdentitySettings:ApiSettings:documentmanagement-api:ClientId", "value": "DocumentManagement.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:documentmanagement-api:ClientSecret", "value": "y7sKSbkTc3tDa6Hj5jeiqe4oIxXhbYNHa5b3AqR1hTo="}, {"key": "IdentitySettings:ApiSettings:documentsign-api:ClientId", "value": "DocumentSign.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:documentsign-api:ClientSecret", "value": "RgC740/okVKl4LKufLJGTJg4W+5p22FoH5MQoMOlEqk="}, {"key": "IdentitySettings:ApiSettings:driver-assignment-api:ClientId", "value": "DriverAssignment.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:driver-assignment-api:ClientSecret", "value": "zsjv7Y2Ffd0sJWCk/ov1LpBemDRctPaCgONuqlahGbU="}, {"key": "IdentitySettings:ApiSettings:ecu-api:ClientId", "value": "ECU.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:ecu-api:ClientSecret", "value": "s3B+rxjXjNLDnXX+M4vWghudeXx+Y0nqG7IbD9IUXYE="}, {"key": "IdentitySettings:ApiSettings:enatis-rtmc-api:ClientId", "value": "RTMC.eNatis.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:enatis-rtmc-api:ClientSecret", "value": "n40/zLZQnfqMmiv37EBlw1J5IQBtiRzpmFWuhV2DAsU="}, {"key": "IdentitySettings:ApiSettings:evaluation-api:ClientId", "value": "Evaluation.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:evaluation-api:ClientSecret", "value": "kivyLDmh8XKLnIXr6v62ZhtRssCIHjf4unltO/2/UR8="}, {"key": "IdentitySettings:ApiSettings:evolve-api:ClientId", "value": "Evolve.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:evolve-api:ClientSecret", "value": "58PoNM3/p/A/KoFaTAT1pjPvWxh7GwGghd71wa59KkQ="}, {"key": "IdentitySettings:ApiSettings:external-feeds-api:ClientId", "value": "ExternalFeeds.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:external-feeds-api:ClientSecret", "value": "Jck6Y4hZoRlCjYhoutyK3MOWTX0BQ3eR2nivK9yzbcE="}, {"key": "IdentitySettings:ApiSettings:feed-experiments-api:ClientId", "value": "Feed.Experiments.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:feed-experiments-api:ClientSecret", "value": "1TH8TKQVTP1XvQma7TJwdU5k+TfbY+5VXNTqdpNbJI4="}, {"key": "IdentitySettings:ApiSettings:finance-applications-api:ClientId", "value": "FinanceApplication.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:finance-applications-api:ClientSecret", "value": "LAyGlcg/4ZuLQjvqccGn07BS/XKvpn9hh+Azzt0y47s="}, {"key": "IdentitySettings:ApiSettings:financial-api:ClientId", "value": "Financial.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:financial-api:ClientSecret", "value": "TfPxedwgrB5XceoOYKV7h/B04zOC6IS7ZqxqZR+mMzU="}, {"key": "IdentitySettings:ApiSettings:floorplan-api:ClientId", "value": "Floorplan.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:floorplan-api:ClientSecret", "value": "dIFzi5XxctXvtjGc96AgP1N28dw/BEowX+FT070cqxs="}, {"key": "IdentitySettings:ApiSettings:forms-api:ClientId", "value": "Forms-API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:forms-api:ClientSecret", "value": ""}, {"key": "IdentitySettings:ApiSettings:gps-tracking-api:ClientId", "value": "GPS.Tracking.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:gps-tracking-api:ClientSecret", "value": "+EyeNAI6p/0ttgTjuInTQPJHY3TeREj1ruEmGUytge4="}, {"key": "IdentitySettings:ApiSettings:identity-server-api-public:ClientId", "value": "Identity.Server.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:identity-server-api-public:ClientSecret", "value": "tC6sc85X+xP05QTZk5SzMUme09gnrq1Vef6FLp0+qBo="}, {"key": "IdentitySettings:ApiSettings:identity-server-api:ClientId", "value": "Identity.Server.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:identity-server-api:ClientSecret", "value": "tC6sc85X+xP05QTZk5SzMUme09gnrq1Vef6FLp0+qBo="}, {"key": "IdentitySettings:ApiSettings:ims-api:ClientId", "value": "IMS.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:ims-api:ClientSecret", "value": "Fhu16riejxiL8DfLr+MY/MehYAlE1UGFWrDmW6SKkqU="}, {"key": "IdentitySettings:ApiSettings:inspectify-api:ClientId", "value": "Inspectify-API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:inspectify-api:ClientSecret", "value": "0azM2UuqIRpDLY5bKsz6tx/W/vFrK/idLkg8UzY6CK8="}, {"key": "IdentitySettings:ApiSettings:inventory-management-api:ClientId", "value": "IMS.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:inventory-management-api:ClientSecret", "value": "PO3A8kTAgHibAhHrz4vEvH0uJzsDlFEBK8QaH4zdRhs="}, {"key": "IdentitySettings:ApiSettings:kyc-api:ClientId", "value": "KYC.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:kyc-api:ClientSecret", "value": "FROKO5/Y6OBmUPXQKkGZ8qTB+nXtMYi2xOMtW/b+cU4="}, {"key": "IdentitySettings:ApiSettings:logistics-api:ClientId", "value": "Logistics.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:logistics-api:ClientSecret", "value": "rmFur5fVlaYsGLFDoTpv+bIzrWixHyiqkNYzAZ3MSX4="}, {"key": "IdentitySettings:ApiSettings:media-api:ClientId", "value": "Media.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:media-api:ClientSecret", "value": "DMbI8QN/LJCN6C8VE/fco48vp0HqTQPM4fcZla2i1Vg="}, {"key": "IdentitySettings:ApiSettings:mobalyz-api:ClientId", "value": "Mobalyz.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:mobalyz-api:ClientSecret", "value": ""}, {"key": "IdentitySettings:ApiSettings:mobilebuildmanagement-api:ClientId", "value": "MobileBuildManagement.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:mobilebuildmanagement-api:ClientSecret", "value": "WqjIMoJi8SlpknM4a4445VyzhnnHL6ZzOFVl9aSmi4I="}, {"key": "IdentitySettings:ApiSettings:natis-api:ClientId", "value": "Natis.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:natis-api:ClientSecret", "value": "LX3ZSp0FKdV55Jv86cNDrtTkUXlrnxmWM0aFsL4WpEQ="}, {"key": "IdentitySettings:ApiSettings:netstar-api:ClientId", "value": "Netstar.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:netstar-api:ClientSecret", "value": "pjNBqpdBQReiXoBfLGJeg44swnNieIQ5UHkXNgeZkMA="}, {"key": "IdentitySettings:ApiSettings:nexus-api:ClientId", "value": "Nexus.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:nexus-api:ClientSecret", "value": "tQE9rDJgOxhyMQkkmqEqR6aeb78hzmUIy+wc6+0JDHA="}, {"key": "IdentitySettings:ApiSettings:odometer-management-api:ClientId", "value": "Odometer.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:odometer-management-api:ClientSecret", "value": "HShuIR/Is9jRXNJeydGKzcow1umMBApA7yIZJ4QxZ6E="}, {"key": "IdentitySettings:ApiSettings:organization-api:ClientId", "value": "Organization.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:organization-api:ClientSecret", "value": ""}, {"key": "IdentitySettings:ApiSettings:outsurance-api:ClientId", "value": "OUTsurance.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:outsurance-api:ClientSecret", "value": "Yq3Kv8EQjTdaxk1qI7PW+5DsMtHe2ZFCckHcbC7uXgU="}, {"key": "IdentitySettings:ApiSettings:photo-booth-api:ClientId", "value": "PhotoBooth.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:photo-booth-api:ClientSecret", "value": "dCBMLuyyuwjD56YODDUUAM82NIklil0Z5sIDLmz9Bl8="}, {"key": "IdentitySettings:ApiSettings:pricecards-api:ClientId", "value": "PriceCards.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:pricecards-api:ClientSecret", "value": "ND7Tb5P25Ahe3pLef/I73qX6ROtf30/k8fk0knqcWYY="}, {"key": "IdentitySettings:ApiSettings:pricing-api:ClientId", "value": "Pricing.API.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:pricing-api:ClientSecret", "value": "vgutKng1gmcNvPwFUXwgMf2pYZjrqdenCVgslN26h1w="}, {"key": "IdentitySettings:ApiSettings:processengine:ClientId", "value": "ProcessEngine.B2B.Client"}, {"key": "IdentitySettings:ApiSettings:processengine:ClientSecret", "value": "jaNETVi9JeiOR0uQYlb9NZyWh2cGu6LoBsQQmaHBhak="}, {"key": "IdentitySettings:ApiSettings:reporting-api:ClientId", "value": "Reporting.API.B2B.Client"}]